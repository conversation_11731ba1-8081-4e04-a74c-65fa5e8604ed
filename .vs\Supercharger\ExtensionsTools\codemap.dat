<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectAllTimeMostUsedData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETConfig#GetConfigDirectory</CodeMapItemPath><ParameterList>string, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>AIClient#AIClient</CodeMapItemPath><ParameterList>IAILogger, IAIErrorHandler, IAIConfigManager</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETCombinedLicenseProvider#NetworkLicenseUpdated</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETCombinedLicenseProvider#ETCombinedLicenseProvider</CodeMapItemPath><ParameterList>string, string, string, IETLicenseCryptoService, string, int</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETCombinedLicenseProvider#GetLicenseInfoAsync</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETCombinedLicenseProvider#ForceRefreshAsync</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETGPS#经纬度#FindClosestPoints</CodeMapItemPath><ParameterList>List&lt;XlGpsPointAndRange&gt;, XlGpsPoint, int</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#IsDebugEnabled</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#LogPath</CodeMapItemPath><ParameterList/><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Info</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Info</CodeMapItemPath><ParameterList>object, string</ParameterList><UsageCount>8</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><ParameterList>object, string</ParameterList><UsageCount>12</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><ParameterList>string, Exception</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><ParameterList>object, string, Exception</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Warning</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Warning</CodeMapItemPath><ParameterList>object, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Warning</CodeMapItemPath><ParameterList>string, Exception</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>object, string</ParameterList><UsageCount>13</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>string, Exception</ParameterList><UsageCount>20</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>object, string, Exception</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>object, Exception</ParameterList><UsageCount>21</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>Exception</ParameterList><UsageCount>8</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><ParameterList>ETException</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#LogToTextBox</CodeMapItemPath><ParameterList>TextBox, string, string, bool</ParameterList><UsageCount>9</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLogManager#LogToMultipleTextBoxes</CodeMapItemPath><ParameterList>TextBox, TextBox, string, string, bool, bool</ParameterList><UsageCount>8</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETDateHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETDateHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseController#ETLicenseController</CodeMapItemPath><ParameterList>IETLicenseProvider, IETMachineCodeProvider, IETLocationService, int, string, string[]</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseController#GetCurrentMachineCode</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseController#ForceRefreshAllAsync</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseController#CheckPermissionWithExpireTimeAsync</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTime.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTime.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETIniFile#增强功能#GetSection</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETLoginWebBrowser#公共属性#HeadersJson</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLoginWebBrowser#事件处理#WebView_CoreWebView2InitializationCompleted</CodeMapItemPath><ParameterList>object, CoreWebView2InitializationCompletedEventArgs</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLoginWebBrowser#事件处理#WebView_WebResourceRequested</CodeMapItemPath><ParameterList>object, CoreWebView2WebResourceRequestedEventArgs</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLoginWebBrowser#事件处理#WebView_NavigationCompleted</CodeMapItemPath><ParameterList>object, CoreWebView2NavigationCompletedEventArgs</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLoginWebBrowser#事件处理#ETLoginWebBrowser_FormClosing</CodeMapItemPath><ParameterList>object, FormClosingEventArgs</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETInitializerExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETInitializerExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETUcFileSelect#AutoFillLatestValue</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>AILogger#LogInfo</CodeMapItemPath><ParameterList>string, object[]</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AILogger#LogAPICall</CodeMapItemPath><ParameterList>string, string, TimeSpan, bool, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#AIFileProcessor</CodeMapItemPath><ParameterList>IAILogger, IAIConfigManager</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#CleanupUploadedFilesAsync</CodeMapItemPath><ParameterList>List&lt;string&gt;</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#ReadPdfContentAsync</CodeMapItemPath><ParameterList>string, CancellationToken</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#ReadDocxContentAsync</CodeMapItemPath><ParameterList>string, CancellationToken</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#ReadHtmlContentAsync</CodeMapItemPath><ParameterList>string, CancellationToken</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#GetMimeType</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#ValidateFileIntegrity</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#ValidateFileForUpload</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>AIFileProcessor#AnalyzeUploadError</CodeMapItemPath><ParameterList>Exception, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>HHExcelExtensions#Resize</CodeMapItemPath><ParameterList>Range</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>HHExcelExtensions#OptimizeRangeSize</CodeMapItemPath><ParameterList>Range</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLocationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLocationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#LoadComboBox</CodeMapItemPath><ParameterList>ComboBox, IEnumerable&lt;string&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#LoadComboBox</CodeMapItemPath><ParameterList>ComboBox, string, string</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#LoadContextMenuStrip</CodeMapItemPath><ParameterList>ContextMenuStrip, Dictionary&lt;string, string[]&gt;, TextBox</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#LoadContextMenuStripFromConfig</CodeMapItemPath><ParameterList>ContextMenuStrip, string, TextBox</ParameterList><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#AddConfigManagementButtons</CodeMapItemPath><ParameterList>object, string, object, System.Action</ParameterList><UsageCount>5</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#CheckedListBoxItemInvalid</CodeMapItemPath><ParameterList>CheckedListBox, string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#AddMessageToListview</CodeMapItemPath><ParameterList>ListView, string, bool</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#HideTabPage</CodeMapItemPath><ParameterList>TabControl, TabPage</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#填充控件#ShowTabPage</CodeMapItemPath><ParameterList>TabControl, TabPage</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#控件属性设置#EnabledInvoke</CodeMapItemPath><ParameterList>Control, bool</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#控件属性设置#VisibleInvoke</CodeMapItemPath><ParameterList>Control, bool</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#控件属性设置#TextInvoke</CodeMapItemPath><ParameterList>Button, string</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#控件属性设置#ClearInvoke</CodeMapItemPath><ParameterList>TextBox</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#操作配置文件#BindWindowsFormControl</CodeMapItemPath><ParameterList>dynamic, ETIniFile, string, string, bool</ParameterList><UsageCount>13</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#操作配置文件#BindComboBox</CodeMapItemPath><ParameterList>ComboBox, int, bool</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#操作配置文件#BindComboBox</CodeMapItemPath><ParameterList>ComboBox, string, int, bool</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#操作配置文件#BindComboBox</CodeMapItemPath><ParameterList>ComboBox, ETIniFile, string, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#TextBox历史记录管理#BindTextBox</CodeMapItemPath><ParameterList>TextBox, int, bool</ParameterList><UsageCount>6</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETForm#TextBox历史记录管理#CreateTextBoxContextMenu</CodeMapItemPath><ParameterList>TextBox, string, int</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#DEFAULT_BASE_INFO_PATH</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#ConfigFilePath</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#GetBaseInfoPath</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#SetBaseInfoPath</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#LaunchLicenseGenerator</CodeMapItemPath><ParameterList/><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#LaunchLicenseGenerator</CodeMapItemPath><ParameterList>string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#CreateLicenseControllerFromFile</CodeMapItemPath><ParameterList>string, string, string, string[]</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#CreateCombinedLicenseController</CodeMapItemPath><ParameterList>string, string, string, string, string, string, string[], ET.ETLicense.NetworkLicenseUpdatedEventHandler, int</ParameterList><UsageCount>4</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#ETBaseConfig#AdminPassword</CodeMapItemPath><ParameterList/><UsageCount>1</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#LoadBaseConfig</CodeMapItemPath><ParameterList>string, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETLicenseManager#SaveBaseConfig</CodeMapItemPath><ParameterList>ETBaseConfig, string, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETControlMappingGenerator#构造函数#ETControlMappingGenerator</CodeMapItemPath><ParameterList>object, Type, BindingFlags</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#GetControlStructure</CodeMapItemPath><ParameterList>Func&lt;Type, bool&gt;</ParameterList><UsageCount>3</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#CreateControlInfo</CodeMapItemPath><ParameterList>string, object, Type</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#GetControlLabel</CodeMapItemPath><ParameterList>object</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#GetControlParent</CodeMapItemPath><ParameterList>object</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETFile.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETFile#路径操作#ApplicationPath</CodeMapItemPath><ParameterList>string, PathType, string</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems><AllTimeMostUsedDataItem><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindColumnsByHeadersInternal</CodeMapItemPath><ParameterList>string, object, bool, Action&lt;string&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindHeadersInRangeCore</CodeMapItemPath><ParameterList>object, string[], bool, Action&lt;string&gt;</ParameterList><UsageCount>2</UsageCount></AllTimeMostUsedDataItem><AllTimeMostUsedDataItem><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindHeadersInRange</CodeMapItemPath><ParameterList>Range, string[], bool, Action&lt;string&gt;</ParameterList><UsageCount>1</UsageCount></AllTimeMostUsedDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfAllTimeMostUsedDataItemIPg14zztYaWdJY9w></ProjectAllTimeMostUsedData><ProjectExpandedStateData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#权限映射注册</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#权限检查</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#标题混淆功能</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#缓存管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#控件信息查询</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#批量操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#配置管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#调试和诊断</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETControlPermissionManager#批量控件标题更新功能</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#格式</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#设置边框</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#设置警示色</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETUcFileSelect#PathSelectedHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETUcFileSelect#历史记录管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>NetworkLicenseUpdatedEventHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETAutoCollapseWindowBehavior#常量</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETAutoCollapseWindowBehavior#字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETAutoCollapseWindowBehavior#属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETAutoCollapseWindowBehavior#构造函数和析构函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETAutoCollapseWindowBehavior#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETAutoCollapseWindowBehavior#消息过滤器</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETGPS#KML</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETGPS#度分秒转换</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETGPS#计算距离</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>GPS struct</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLogManager#Warn方法（过期版本，建议使用Warning方法）</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETDateHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETDateHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETWebBrowserJsonFormatter#数据模型</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETWebBrowserJsonFormatter#核心方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETWebBrowserJsonFormatter#格式验证</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETAboutLicenseForm#安全验证方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTime.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTime.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETSectionConfigReader#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETSectionConfigReader#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETSectionConfigReader#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETSectionConfigReader#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETSectionConfigReader#测试方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共事件</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#公共属性</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#初始化方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#标头操作方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#辅助方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>数据模型类</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLoginWebBrowser#Windows Form Designer generated code</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETUIPermissionManager#控件权限管理功能</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETUIPermissionManager#控件标题管理便捷接口</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETInitializerExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETInitializerExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETUcFileSelect#PathSelectedHandler</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETUcFileSelect#初始化</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#常量定义</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#受保护字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#抽象属性和方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#权限管理器管理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#网络授权更新回调处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#权限检查方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#权限刷新方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLicenseManagerBase#初始化和清理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETInputDialog#私有字段</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETInputDialog#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETInputDialog#私有方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETInputDialog#事件处理</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETInputDialog#静态方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证邮箱</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证网址</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证日期</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证手机号</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证IP</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证身份证是否有效</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#是不是Int型的</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#看字符串的长度是不是在限定数之间 一个中文为两个字符</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#是不是中国电话，格式010-85849685</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#邮政编码 6个数字</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证是不是正常字符 字母，数字，下划线的组合</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证用户名：必须以字母开头，可以包含字母、数字、“_”、“.”，至少5个字符</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证是否为小数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证年月日</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证日期格式</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证后缀名</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证字符是否在4至12之间</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#判断字符串是否为数字</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#是否为数字型</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ValidatorHelper#验证是否包含汉语/全部汉语</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#GetWorkbook</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#GetWorksheet</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#WorksheetAlias</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#GetRange</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#GetRange2</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>HHExcelExtensions#获取/设置值</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLocationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLocationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETForm#窗体消息传递</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#窗体操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#获得单元格坐标</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#Excel窗体操作</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#从控件取值</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#控件属性设置</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#Log</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#DllImport</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETForm#Win32 API Wrappers</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#日志级别枚举</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#构造函数</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem><ExpandedStateDataItem><CodeMapItemPath>ETLogDisplayControl#公共方法</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETControlMappingGenerator#标题映射生成</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETFile.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETFile#LongPath</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems><ExpandedStateDataItem><CodeMapItemPath>ETRangeSelectControl#组件设计器生成的代码</CodeMapItemPath><IsExpanded>false</IsExpanded><ParameterList/></ExpandedStateDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfExpandedStateDataItemIPg14zztYaWdJY9w></ProjectExpandedStateData><ProjectFavoriteData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETDateHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETDateHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTime.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTime.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETInitializerExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETInitializerExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLocationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLocationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfFavoriteDataItemIPg14zztYaWdJY9w></ProjectFavoriteData><ProjectHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>903</Char><CodeMapItemPath>ETControlPermissionManager#权限映射注册#RegisterControlPermissionMapping</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-02T10:55:08.4925937+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>56</Char><CodeMapItemPath>ETConfig#GetETConfigIniFilePath</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-27T14:17:58.3041633+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETConfig#GetConfigDirectory</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:24:12.0134757+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>59</Char><CodeMapItemPath>HHExcelExtensions#设置背景色#Formate设置背景色</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T21:46:21.3650795+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, EnumColorNum</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>4</Char><CodeMapItemPath>ETUcFileSelect#Text</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-03T23:32:20.2565435+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUcFileSelect#初始化#ETUcFileSelect</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T23:47:58.9857296+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>465</Char><CodeMapItemPath>ETLoginWebBrowserFactory#GetHeadersJson</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T21:40:23.981776+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Form</ParameterList></HistoryDataItem><HistoryDataItem><Char>664</Char><CodeMapItemPath>ETLoginWebBrowserFactory#ShowLoginBrowser</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-04T22:52:03.9919365+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Form, Action&lt;string&gt;, Action, Action&lt;Exception&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>219</Char><CodeMapItemPath>ETExcelExtensions#IsCellNonText</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T13:49:52.9552579+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1170</Char><CodeMapItemPath>AIProcessingManager#ProcessAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-22T14:57:55.3015539+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIDataSourceConfig, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>1887</Char><CodeMapItemPath>AIProcessingManager#ProcessInBatchesAsync</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-21T21:20:13.9303373+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIRequest, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>215</Char><CodeMapItemPath>AIProcessingManager#FillResultsAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-21T22:06:04.8778259+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIResponse, AIDataSourceConfig, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>674</Char><CodeMapItemPath>AIProcessingManager#ProcessFilesAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T15:56:31.6398231+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;AIDataGroup&gt;, AIDataSourceConfig, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>774</Char><CodeMapItemPath>AIProcessingManager#CleanupResourcesAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T16:01:29.7064223+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;AIDataGroup&gt;, AIDataSourceConfig</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>AIClient#AIClient</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-22T16:16:24.5409321+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>IAILogger, IAIErrorHandler, IAIConfigManager</ParameterList></HistoryDataItem><HistoryDataItem><Char>199</Char><CodeMapItemPath>AIClient#SendChatRequestAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T21:13:01.3295719+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIRequest, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>1098</Char><CodeMapItemPath>AIClient#FindMatchingBracket</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-24T14:47:08.6719923+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, int, char, char</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>132</Char><CodeMapItemPath>ETRangeSelectControl#事件处理#button选择_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T22:23:46.3592443+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETRangeSelectControl#私有方法#ShowSelectionBox</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T22:19:52.0805821+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>976</Char><CodeMapItemPath>ETRemoteLicenseProvider#GetLicenseInfoAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-29T12:56:29.0778517+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>493</Char><CodeMapItemPath>ETRemoteLicenseProvider#GetDataWithRetryAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T13:00:12.0515661+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>880</Char><CodeMapItemPath>ETCombinedLicenseProvider#GetLicenseInfoAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-21T09:26:42.2063455+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>2611</Char><CodeMapItemPath>ETCombinedLicenseProvider#UpdateNetworkLicenseInfoAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-26T23:47:40.5602051+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>806</Char><CodeMapItemPath>ETCombinedLicenseProvider#GetNetworkLicenseWithTimeoutAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-21T09:36:34.3153747+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>IETLicenseProvider, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETCombinedLicenseProvider#ForceRefreshAsync</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-21T09:26:39.1980731+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>557</Char><CodeMapItemPath>ETAutoCollapseWindowBehavior#私有方法#ShowFullWindow</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-20T21:51:05.6786538+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETGPS#经纬度#FindClosestPoints</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-20T20:29:34.7471208+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;XlGpsPointAndRange&gt;, XlGpsPoint, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>312</Char><CodeMapItemPath>ETLoginWebBrowserTest#TestRefreshCookies</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-04T23:01:29.8797856+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>417</Char><CodeMapItemPath>ETLoginWebBrowserTest#ShowHeaderDetails</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-04T23:14:13.0572288+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>227</Char><CodeMapItemPath>ETLoginWebBrowserTest#GetHeaderSummary</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-04T23:14:25.1848549+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>46</Char><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-24T22:22:18.1190824+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-24T22:22:18.9272606+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Exception</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLogManager#Debug</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-24T22:22:19.3581683+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, string, Exception</ParameterList></HistoryDataItem><HistoryDataItem><Char>7</Char><CodeMapItemPath>ETLogManager#Warning</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-13T22:25:58.0434234+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLogManager#Warning</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-24T22:25:35.1573459+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Exception</ParameterList></HistoryDataItem><HistoryDataItem><Char>5</Char><CodeMapItemPath>ETLogManager#Error</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T23:21:24.0154264+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, string, Exception</ParameterList></HistoryDataItem><HistoryDataItem><Char>964</Char><CodeMapItemPath>ETLogManager#WriteLog</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-18T16:44:08.5430627+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, object, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETDateHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETDateHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>173</Char><CodeMapItemPath>ETWebBrowserJsonFormatter#核心方法#CreateLoginInfoJsonFromWebViewAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T22:17:13.2255545+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2, Dictionary&lt;string, string&gt;, HeadersOptions</ParameterList></HistoryDataItem><HistoryDataItem><Char>1190</Char><CodeMapItemPath>ETWebBrowserJsonFormatter#辅助方法#GetCookiesFromWebView2Async</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-09T10:58:17.4932459+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>WebView2</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLicenseController#ETLicenseController</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-21T09:26:11.1245149+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>IETLicenseProvider, IETMachineCodeProvider, IETLocationService, int, string, string[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>33</Char><CodeMapItemPath>ETLicenseController#ForceRefreshAllAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-21T09:26:14.3119546+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>340</Char><CodeMapItemPath>ETLicenseController#ForceRefreshAllWithCallbackAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T20:43:39.6336498+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>51</Char><CodeMapItemPath>ETLicenseController#HasPermissionAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-26T23:14:26.300334+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>272</Char><CodeMapItemPath>ETLicenseController#CheckPermissionWithExpireTimeAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-21T09:26:11.8655108+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>75</Char><CodeMapItemPath>ETAboutLicenseForm#btnUpdateLicense_Click</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T20:43:27.8004352+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTime.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTime.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETThreadSafeCallbackHelper#SafeInvokeNetworkLicenseUpdated</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-26T23:47:51.3065721+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>NetworkLicenseUpdatedEventHandler, ETLicenseInfo, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>34</Char><CodeMapItemPath>AIRequest#AIRequest</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-21T20:30:30.7270729+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>137</Char><CodeMapItemPath>AIModelConfig#GetEffectiveBaseUrl</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T21:02:04.8822061+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>61</Char><CodeMapItemPath>ETExcelExtensions#SaveCopyAs</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-28T18:36:55.7337894+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Workbook, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IExcelApplicationProvider#GetExcelApplication</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T22:20:46.5939047+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLicenseGenerator#LoadFromOssAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-20T22:52:36.0511099+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>195</Char><CodeMapItemPath>ETExcelExtensions#CopyWorksheetFromTemplateToWorkbook</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:10:32.1792409+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Workbook, bool, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>73</Char><CodeMapItemPath>ProcessingProgress#ProcessingProgress</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-21T20:27:59.6308837+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>124</Char><CodeMapItemPath>ProcessingProgress#ProcessingProgress</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-21T20:30:45.8005242+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, int, string, int, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>945</Char><CodeMapItemPath>ETLicenseGeneratorForm#OpenLicenseFromOss</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-20T22:51:57.1688739+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>72</Char><CodeMapItemPath>ETLicenseGeneratorForm#btnModifyEntity_Click</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-28T15:43:25.0057332+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>652</Char><CodeMapItemPath>ETLicenseGeneratorForm#WriteFileSecurely</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T12:56:37.2082401+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, byte[]</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1726</Char><CodeMapItemPath>ETSectionConfigReader#测试方法#TestConfigReader</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-19T16:16:01.3374691+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>145</Char><CodeMapItemPath>ETIniFile#IniReadValue</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-04T12:15:36.8516572+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETIniFile#增强功能#GetSection</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-13T20:56:58.598498+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>71</Char><CodeMapItemPath>ETIniFile#兼容旧版本的方法#GetValue</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-03T21:00:32.9726974+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1843</Char><CodeMapItemPath>AIExcelAssistant#公共API方法#ProcessExcelDataAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:15:52.3265494+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, Range, Range, Range, string, string, DataSourceMode, FileProcessingMode, bool, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLoginWebBrowser#事件处理#WebView_WebResourceRequested</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-07T15:58:27.1142167+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, CoreWebView2WebResourceRequestedEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>1748</Char><CodeMapItemPath>ETLoginWebBrowser#事件处理#IsApiRequest</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-07T14:42:43.1953806+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, CoreWebView2WebResourceContext</ParameterList></HistoryDataItem><HistoryDataItem><Char>425</Char><CodeMapItemPath>ETLoginWebBrowser#事件处理#WebView_NavigationCompleted</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-16T22:11:45.6332821+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, CoreWebView2NavigationCompletedEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>137</Char><CodeMapItemPath>ETLoginWebBrowser#事件处理#ETLoginWebBrowser_Load</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-16T22:01:55.6804387+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, EventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>626</Char><CodeMapItemPath>ETLoginWebBrowser#事件处理#ETLoginWebBrowser_FormClosing</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-12T22:30:46.1721124+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, FormClosingEventArgs</ParameterList></HistoryDataItem><HistoryDataItem><Char>801</Char><CodeMapItemPath>ETLoginWebBrowser#标头操作方法#RefreshCookiesAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-09T10:51:17.136042+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLoginWebBrowser#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-03T20:24:21.0130382+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUIPermissionManager#SetDefaultUIVisibility</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-30T23:27:07.2711018+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUIPermissionManager#HasPermission</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-02T21:12:17.2570959+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUIPermissionManager#CreateUIActions</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-24T10:39:16.9850936+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Action&lt;bool&gt;[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUIPermissionManager#控件标题管理便捷接口#GetControlDisplayTitle</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-01T23:42:27.6667432+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>1069</Char><CodeMapItemPath>ETUIPermissionManager#批量控件标题更新功能（从项目特定代码抽取的通用功能）#BatchUpdateControlTitles</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-02T10:54:32.7640725+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Dictionary&lt;string, object&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>IAIProcessingManager#ProcessAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T21:02:04.7978672+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIDataSourceConfig, IProgress&lt;ProcessingProgress&gt;, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IAIFileProcessor#UploadFileAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T16:11:08.0177023+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IAIClient#SendRequestAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-21T20:35:35.747183+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIRequest, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IAIResultFiller#FillResultsAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-21T21:43:52.2029314+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIResponse, AIDataSourceConfig</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETInitializerExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETInitializerExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETPermissionManager#HasPermission</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-02T21:12:19.3720446+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>39</Char><CodeMapItemPath>ETPermissionManager#ForceRefreshPermissionsAndUI</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-30T19:19:25.1063069+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUcFileSelect#AutoFillLatestValue</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-28T14:23:35.5463179+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETUcFileSelect#历史记录管理#GetHistoryFilePath</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-28T18:26:51.5955634+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>198</Char><CodeMapItemPath>ETStringPrefixSuffixProcessor#Initialize</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:07:19.1097293+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>327</Char><CodeMapItemPath>AILogger#LogInfo</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-22T13:54:04.8214972+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, object[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>193</Char><CodeMapItemPath>AILogger#LogWarning</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T13:54:24.2579625+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, object[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>208</Char><CodeMapItemPath>AILogger#LogDebug</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:38:42.8901724+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, object[]</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>AILogger#LogAPICall</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-21T22:15:55.1468762+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, TimeSpan, bool, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>862</Char><CodeMapItemPath>ETLicenseManagerBase#授权控制器管理#InitializeLicenseController</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-01T23:17:02.6662865+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>268</Char><CodeMapItemPath>ETInputDialog#公共属性#MaxPromptWidth</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T22:12:46.5935861+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>AIFileProcessor#AIFileProcessor</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T20:55:47.5027725+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>IAILogger, IAIConfigManager</ParameterList></HistoryDataItem><HistoryDataItem><Char>15</Char><CodeMapItemPath>AIFileProcessor#UploadFileAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:33:03.0308452+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>46</Char><CodeMapItemPath>AIFileProcessor#UploadFileAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T15:32:42.472471+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, AIModelConfig, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>1147</Char><CodeMapItemPath>AIFileProcessor#ProcessFilesAsync</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-22T19:55:37.1204678+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;string&gt;, FileProcessingMode, AIModelConfig, CancellationToken</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>AIFileProcessor#CleanupUploadedFilesAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T20:54:40.8897938+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>List&lt;string&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>AIFileProcessor#ValidateFileForUpload</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-22T15:24:47.7698145+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>AIFileProcessor#AnalyzeUploadError</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T15:24:52.8736764+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Exception, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>22</Char><CodeMapItemPath>AIFileProcessor#CreateOpenAIFileClient</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-22T19:59:47.903691+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIModelConfig</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>84</Char><CodeMapItemPath>ValidatorHelper#中文#IsChinese</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:21:26.4045938+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>AIResultFiller#FillResultsAsync</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-21T21:44:30.5963802+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>AIResponse, AIDataSourceConfig</ParameterList></HistoryDataItem><HistoryDataItem><Char>584</Char><CodeMapItemPath>AIResultFiller#ConvertCellValue</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-22T15:37:39.1087788+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object</ParameterList></HistoryDataItem><HistoryDataItem><Char>307</Char><CodeMapItemPath>AIResultFiller#SetCellFormat</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-21T21:46:00.7686261+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, object</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>HHExcelExtensions#Resize</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T14:21:36.5291485+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range</ParameterList></HistoryDataItem><HistoryDataItem><Char>135</Char><CodeMapItemPath>HHExcelExtensions#OptimizeRangeSize</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-17T14:21:40.4686745+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>639</Char><CodeMapItemPath>ETOssService#ETOssService</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-20T22:42:46.881145+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ETOssConfig, IETLicenseCryptoService</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>IETOssService#GetLicenseFromOssAsync</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-20T22:52:39.322422+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLocationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLocationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETForm#填充控件#LoadComboBox</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-30T22:33:57.9809781+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ComboBox, IEnumerable&lt;string&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETForm#填充控件#LoadComboBox</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T22:34:03.9506139+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ComboBox, string, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETForm#填充控件#LoadContextMenuStrip</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T23:00:31.9273202+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ContextMenuStrip, Dictionary&lt;string, string[]&gt;, TextBox</ParameterList></HistoryDataItem><HistoryDataItem><Char>326</Char><CodeMapItemPath>ETForm#填充控件#LoadContextMenuStripFromConfig</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T23:00:33.3198542+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ContextMenuStrip, string, TextBox</ParameterList></HistoryDataItem><HistoryDataItem><Char>26</Char><CodeMapItemPath>ETForm#填充控件#AddConfigManagementButtons</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-30T22:51:48.0832928+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, string, object, System.Action</ParameterList></HistoryDataItem><HistoryDataItem><Char>240</Char><CodeMapItemPath>ETForm#填充控件#HandleConfigManagementOperation</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T22:53:48.0347893+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, System.Action, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETForm#填充控件#CheckedListBoxItemInvalid</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T22:41:37.4657896+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>CheckedListBox, string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETForm#操作配置文件#BindWindowsFormControl</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-30T22:34:27.6699085+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>dynamic, ETIniFile, string, string, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETForm#操作配置文件#BindComboBox</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T22:34:16.1493488+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>ComboBox, int, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>11</Char><CodeMapItemPath>ETForm#TextBox历史记录管理#BindTextBox</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-30T11:59:22.167569+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>TextBox, int, bool</ParameterList></HistoryDataItem><HistoryDataItem><Char>2471</Char><CodeMapItemPath>ETForm#TextBox历史记录管理#CreateTextBoxContextMenu</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-30T11:59:22.9705883+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>TextBox, string, int</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLicenseManager#ConfigFilePath</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-06-21T09:25:44.9874557+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLicenseManager#SetBaseInfoPath</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-06-21T09:25:43.613125+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLicenseManager#CreateCombinedLicenseController</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-26T22:56:39.3613548+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, string, string, string, string, string, string[], ET.ETLicense.NetworkLicenseUpdatedEventHandler, int</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETLicenseManager#ETBaseConfig#AdminPassword</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-06-21T09:25:52.9104573+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>668</Char><CodeMapItemPath>ETLicenseManager#WriteFileSecurely</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-29T13:00:12.1757509+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, byte[]</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>737</Char><CodeMapItemPath>ETExcelDataSummarizer#SummarizeData</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T11:59:48.9480805+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, Range, List&lt;string&gt;, SummarizeOptions</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETNotificationHelper#ShowNotification</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-30T09:12:42.4795401+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, bool, bool, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>270</Char><CodeMapItemPath>ETLogDisplayControl#私有方法#InitializeLogDisplay</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T22:02:44.1530994+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1448</Char><CodeMapItemPath>ETOssBrowserForm#ListObjects</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-28T16:07:12.8675769+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETControlMappingGenerator#构造函数#ETControlMappingGenerator</CodeMapItemPath><HistoryLevel>5</HistoryLevel><HistoryTimeStamp>2025-07-01T09:52:22.8234838+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, Type, BindingFlags</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#GetControlStructure</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-01T09:53:25.9943221+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Func&lt;Type, bool&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#CreateControlInfo</CodeMapItemPath><HistoryLevel>4</HistoryLevel><HistoryTimeStamp>2025-07-01T09:53:20.6660913+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, object, Type</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#GetControlLabel</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-01T09:53:22.9269889+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETControlMappingGenerator#控件结构获取#GetControlParent</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-01T09:53:24.6673006+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETFile.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>1222</Char><CodeMapItemPath>ETFile#路径操作#ApplicationPath</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-06-21T16:06:46.6741026+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, PathType, string</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>103</Char><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindColumnByHeaderTitle</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-07-23T14:39:32.6870209+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, Range, Action&lt;string&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>0</Char><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindColumnsByHeadersInternal</CodeMapItemPath><HistoryLevel>3</HistoryLevel><HistoryTimeStamp>2025-07-23T12:00:47.5226368+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>string, object, bool, Action&lt;string&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>54</Char><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindHeadersInRangeCore</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T14:25:15.6286452+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>object, string[], bool, Action&lt;string&gt;</ParameterList></HistoryDataItem><HistoryDataItem><Char>540</Char><CodeMapItemPath>ETExcelExtensions#筛选/标题行#FindHeadersInRange</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-23T14:41:01.5850557+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>Range, string[], bool, Action&lt;string&gt;</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>33</Char><CodeMapItemPath>ETRangeSelectControl#Dispose</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-22T23:53:19.6256965+08:00</HistoryTimeStamp><Line>0</Line><ParameterList>bool</ParameterList></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems><HistoryDataItem><Char>164</Char><CodeMapItemPath>AIConfigManager#AIConfigManager</CodeMapItemPath><HistoryLevel>1</HistoryLevel><HistoryTimeStamp>2025-07-27T14:02:32.34417+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem><HistoryDataItem><Char>541</Char><CodeMapItemPath>AIConfigManager#GetBaseUrlAsync</CodeMapItemPath><HistoryLevel>2</HistoryLevel><HistoryTimeStamp>2025-06-22T15:38:33.0911776+08:00</HistoryTimeStamp><Line>0</Line><ParameterList/></HistoryDataItem></DataItems><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfHistoryDataItemIPg14zztYaWdJY9w></ProjectHistoryData><ProjectInCodeHighlightData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETDateHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETDateHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTime.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTime.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETInitializerExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETInitializerExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLocationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLocationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfInCodeHighlightDataItemIPg14zztYaWdJY9w></ProjectInCodeHighlightData><ProjectMiniViewData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Format.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserFactory.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Conversion.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIProcessingManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIClient.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIDataExtractor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\FileValidationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAutoCollapseWindowBehavior.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIResponseModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETString.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETString.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETGeographic\ETGPS.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETGeographic\ETGPS.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowserTest.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLogManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLogManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETLogDisplayControl使用示例.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETDateHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETDateHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETWebBrowserJsonFormatter.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseController.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseController.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETAboutLicenseForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelAutoUpdate2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTime.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTime.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETThreadSafeCallbackHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIRequestModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorkbookOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETAutoResetLabel.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETAutoResetLabel.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelConfig.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_Core.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\IExcelApplicationProvider.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\IExcelApplicationProvider.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_WorksheetOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Models\AIDataModels.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseGeneratorForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETSectionConfigReader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETSectionConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETIniFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETIniFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\AIExcelAssistant.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\Controls\ETRangeSelectControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionNamesForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseAdminLoginForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLoginWebBrowser\ETLoginWebBrowser.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Interfaces\IAIInterfaces.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETInitializerExample.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETInitializerExample.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETPermissionManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETStringPrefixSuffixProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETUcFileSelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Exceptions\AIExceptions.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AILogger.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AILogger.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManagerBase.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETInputDialog.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETInputDialog.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIFileProcessor.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\JsonSchemaHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_CellComments.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>Common.Utility\ValidatorHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>Common.Utility\ValidatorHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Services\Core\AIResultFiller.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_GetObjects.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLocationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLocationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Constants\AIConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIErrorHandler.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETLicenseManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelDataSummarizer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETCommunicationService\ETCommunicationService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETTools\ETNotificationHelper.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETTools\ETNotificationHelper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETLogDisplayControl.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETLogDisplayControl.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETOssBrowserForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETLicense\ETControlMappingGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETFile.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETFile.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETExcel\ETExcelExtensions_FilterAndHeader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\HHUcDirectorySelect.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\Controls\ETRangeSelectControl.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w><a:Key>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</a:Key><a:Value><DataItems/><ProjectItemFileName>ExtensionsTools\ETAIv2\Utils\AIConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDataOfMiniViewDataItemIPg14zztYaWdJY9w></ProjectMiniViewData><ProjectName>ExtensionsTools</ProjectName></ProjectData>