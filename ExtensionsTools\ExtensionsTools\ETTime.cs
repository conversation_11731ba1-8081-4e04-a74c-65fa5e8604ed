using System;
using System.IO;

namespace ET
{
    /// <summary>
    /// 时间处理扩展类，提供随机时间生成和文件时间修改功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// 1. 生成指定范围内的随机日期时间
    /// 2. 支持排除特定时间段的随机时间生成
    /// 3. 文件时间属性的修改（创建时间、修改时间）
    /// 4. 跨越午夜的时间段处理
    /// 5. 智能的文件时间一致性维护
    ///
    /// 特色功能：
    /// - 支持避开特定时间段的随机时间生成（如避开工作时间）
    /// - 自动处理跨越午夜的排除时间段
    /// - 智能维护文件时间的逻辑一致性（修改时间不早于创建时间）
    /// - 提供防止无限循环的安全机制
    ///
    /// 使用场景：
    /// - 测试数据生成
    /// - 文件时间戳伪装
    /// - 模拟文件操作历史
    ///
    /// 使用示例：
    /// <code>
    /// // 修改文件的最后修改时间为随机时间（避开工作时间9:00-18:00）
    /// DateTime newTime = HHExcelExtensions.ModifyFileTime(
    ///     "test.txt",
    ///     FileTimeType.LastModifyTime,
    ///     DateTime.Now.AddDays(-30),
    ///     DateTime.Now,
    ///     new TimeSpan(9, 0, 0),  // 排除开始时间
    ///     new TimeSpan(18, 0, 0)  // 排除结束时间
    /// );
    /// </code>
    /// </remarks>
    public static partial class ETExcelExtensions
    {
        /// <summary>
        /// 生成指定范围内且避开特定时间段的随机时间
        /// </summary>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="excludeStartTime">排除时间段的开始时间</param>
        /// <param name="excludeEndTime">排除时间段的结束时间</param>
        /// <returns>生成的随机日期时间</returns>
        /// <remarks>
        /// 此方法会生成一个在指定日期范围内的随机日期时间，同时避开指定的每日排除时间段。
        /// </remarks>
        static DateTime GenerateRandomDateTime(
            DateTime startDate,
            DateTime endDate,
            TimeSpan excludeStartTime,
            TimeSpan excludeEndTime
        )
        {
            // 验证输入参数
            if (startDate >= endDate)
                throw new ArgumentException("开始时间必须小于结束时间");
            if (excludeStartTime.TotalHours is > 24 or < 0 || excludeEndTime.TotalHours is > 24 or < 0)
                throw new ArgumentException("排除时间段必须在0到24小时之间");

            Random random = new();
            double totalSeconds = (endDate - startDate).TotalSeconds;
            DateTime randomDateTime;
            int maxAttempts = 1000; // 设置最大尝试次数，避免无限循环
            int attempts = 0;

            do
            {
                // 生成随机秒数并添加到开始日期
                double randomSeconds = random.NextDouble() * totalSeconds;
                randomDateTime = startDate.AddSeconds(randomSeconds);
                attempts++;

                if (attempts >= maxAttempts)
                {
                    throw new InvalidOperationException("无法在指定条件下生成有效的随机时间，请检查排除时间段设置");
                }
            } while (IsInExcludePeriod(randomDateTime.TimeOfDay, excludeStartTime, excludeEndTime));

            return randomDateTime;
        }

        /// <summary>
        /// 判断给定时间是否在排除时间段内
        /// </summary>
        /// <param name="currentTime">当前时间</param>
        /// <param name="excludeStartTime">排除时间段的开始时间</param>
        /// <param name="excludeEndTime">排除时间段的结束时间</param>
        /// <returns>如果在排除时间段内返回true，否则返回false</returns>
        /// <remarks>
        /// 此方法考虑了跨越午夜的情况，例如从23:00到次日1:00的排除时间段。
        /// </remarks>
        static bool IsInExcludePeriod(
            TimeSpan currentTime,
            TimeSpan excludeStartTime,
            TimeSpan excludeEndTime
        )
        {
            return excludeStartTime < excludeEndTime
                ? currentTime >= excludeStartTime && currentTime <= excludeEndTime
                : currentTime >= excludeStartTime || currentTime <= excludeEndTime;
        }

        /// <summary>
        /// 修改文件的时间属性
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="timeType">要修改的时间类型</param>
        /// <param name="startDate">随机时间范围的开始日期</param>
        /// <param name="endDate">随机时间范围的结束日期</param>
        /// <param name="excludeStartTime">每日排除时间段的开始时间</param>
        /// <param name="excludeEndTime">每日排除时间段的结束时间</param>
        /// <returns>设置的新时间</returns>
        /// <remarks>
        /// 此方法会生成一个随机时间并设置到指定文件的指定时间属性。
        /// 如果修改的是最后修改时间，且新时间早于创建时间，则同时更新创建时间。
        /// </remarks>
        public static DateTime ModifyFileTime(
            string filePath,
            FileTimeType timeType,
            DateTime startDate,
            DateTime endDate,
            TimeSpan excludeStartTime,
            TimeSpan excludeEndTime
        )
        {
            if (!File.Exists(filePath))
                throw new FileNotFoundException("指定的文件不存在", filePath);

            // 生成随机时间
            DateTime targetDateTime = GenerateRandomDateTime(
                startDate,
                endDate,
                excludeStartTime,
                excludeEndTime
            );

            // 根据时间类型设置文件时间
            switch (timeType)
            {
                case FileTimeType.CreateTime:
                    File.SetCreationTime(filePath, targetDateTime);
                    break;

                case FileTimeType.LastModifyTime:
                    DateTime creationTime = File.GetCreationTime(filePath);
                    if (targetDateTime < creationTime)
                    {
                        // 如果新的修改时间早于创建时间，同时更新创建时间
                        File.SetCreationTime(filePath, targetDateTime);
                    }
                    File.SetLastWriteTime(filePath, targetDateTime);
                    break;

                default:
                    throw new ArgumentOutOfRangeException(nameof(timeType), timeType, "不支持的时间类型");
            }

            return targetDateTime;
        }
    }

    /// <summary>
    /// 定义文件时间类型的枚举
    /// </summary>
    public enum FileTimeType
    {
        /// <summary>
        /// 文件的创建时间
        /// </summary>
        CreateTime,

        /// <summary>
        /// 文件的最后修改时间
        /// </summary>
        LastModifyTime,
    }
}
